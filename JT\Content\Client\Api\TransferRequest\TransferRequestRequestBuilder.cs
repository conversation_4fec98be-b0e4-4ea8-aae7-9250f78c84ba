// <auto-generated/>
#pragma warning disable CS0618
using JT.Content.Client.Api.TransferRequest.CreatetransferRequest;
using JT.Content.Client.Api.TransferRequest.Item;
using JT.Content.Client.Api.TransferRequest.Status;
using JT.Content.Client.Api.TransferRequest.User;
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System;
namespace JT.Content.Client.Api.TransferRequest
{
    /// <summary>
    /// Builds and executes requests for operations under \api\TransferRequest
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class TransferRequestRequestBuilder : BaseRequestBuilder
    {
        /// <summary>The createtransferRequest property</summary>
        public global::JT.Content.Client.Api.TransferRequest.CreatetransferRequest.CreatetransferRequestRequestBuilder CreatetransferRequest
        {
            get => new global::JT.Content.Client.Api.TransferRequest.CreatetransferRequest.CreatetransferRequestRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The status property</summary>
        public global::JT.Content.Client.Api.TransferRequest.Status.StatusRequestBuilder Status
        {
            get => new global::JT.Content.Client.Api.TransferRequest.Status.StatusRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The transferRequest property</summary>
        public global::JT.Content.Client.Api.TransferRequest.TransferRequest.TransferRequestRequestBuilder TransferRequest
        {
            get => new global::JT.Content.Client.Api.TransferRequest.TransferRequest.TransferRequestRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The user property</summary>
        public global::JT.Content.Client.Api.TransferRequest.User.UserRequestBuilder User
        {
            get => new global::JT.Content.Client.Api.TransferRequest.User.UserRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>Gets an item from the JT.Content.Client.api.TransferRequest.item collection</summary>
        /// <param name="position">Unique identifier of the item</param>
        /// <returns>A <see cref="global::JT.Content.Client.Api.TransferRequest.Item.TransferRequestItemRequestBuilder"/></returns>
        public global::JT.Content.Client.Api.TransferRequest.Item.TransferRequestItemRequestBuilder this[Guid position]
        {
            get
            {
                var urlTplParams = new Dictionary<string, object>(PathParameters);
                urlTplParams.Add("id", position);
                return new global::JT.Content.Client.Api.TransferRequest.Item.TransferRequestItemRequestBuilder(urlTplParams, RequestAdapter);
            }
        }
        /// <summary>Gets an item from the JT.Content.Client.api.TransferRequest.item collection</summary>
        /// <param name="position">Unique identifier of the item</param>
        /// <returns>A <see cref="global::JT.Content.Client.Api.TransferRequest.Item.TransferRequestItemRequestBuilder"/></returns>
        [Obsolete("This indexer is deprecated and will be removed in the next major version. Use the one with the typed parameter instead.")]
        public global::JT.Content.Client.Api.TransferRequest.Item.TransferRequestItemRequestBuilder this[string position]
        {
            get
            {
                var urlTplParams = new Dictionary<string, object>(PathParameters);
                if (!string.IsNullOrWhiteSpace(position)) urlTplParams.Add("id", position);
                return new global::JT.Content.Client.Api.TransferRequest.Item.TransferRequestItemRequestBuilder(urlTplParams, RequestAdapter);
            }
        }
        /// <summary>
        /// Instantiates a new <see cref="global::JT.Content.Client.Api.TransferRequest.TransferRequestRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public TransferRequestRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/TransferRequest", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::JT.Content.Client.Api.TransferRequest.TransferRequestRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public TransferRequestRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/TransferRequest", rawUrl)
        {
        }
    }
}
#pragma warning restore CS0618
