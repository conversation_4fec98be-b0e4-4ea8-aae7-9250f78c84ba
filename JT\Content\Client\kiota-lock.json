{"descriptionHash": "F708CF831A590A59FBAEF492AE875725C046DA3078FC8811A33A3E243A7AEAA942B06DD3276B6BA782E64B6325BF410737C3B7274FD743D26173420086A8A1C7", "descriptionLocation": "../../Specs/JTApiClient.swagger.json", "lockFileVersion": "1.0.0", "kiotaVersion": "1.27.0", "clientClassName": "JTApiClient", "typeAccessModifier": "Public", "clientNamespaceName": "JT.Content.Client", "language": "CSharp", "usesBackingStore": false, "excludeBackwardCompatible": false, "includeAdditionalData": true, "disableSSLValidation": false, "serializers": ["Microsoft.Kiota.Serialization.Json.JsonSerializationWriterFactory", "Microsoft.Kiota.Serialization.Text.TextSerializationWriterFactory", "Microsoft.Kiota.Serialization.Form.FormSerializationWriterFactory", "Microsoft.Kiota.Serialization.Multipart.MultipartSerializationWriterFactory"], "deserializers": ["Microsoft.Kiota.Serialization.Json.JsonParseNodeFactory", "Microsoft.Kiota.Serialization.Text.TextParseNodeFactory", "Microsoft.Kiota.Serialization.Form.FormParseNodeFactory"], "structuredMimeTypes": ["application/json", "text/plain;q=0.9", "application/x-www-form-urlencoded;q=0.2", "multipart/form-data;q=0.1"], "includePatterns": [], "excludePatterns": [], "disabledValidationRules": []}